#!/bin/bash

# 数据库备份脚本
# 从 database.json 读取数据库连接信息并依次备份

echo "开始备份数据库..."

# 创建备份目录
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "备份目录: $BACKUP_DIR"

# 数据库连接信息
CHAT_DB="postgresql://neondb_owner:<EMAIL>/chat?sslmode=require&channel_binding=require"
CONSOLE_DB="postgresql://neondb_owner:<EMAIL>/console?sslmode=require&channel_binding=require"
ZEPHYR_DB="postgresql://neondb_owner:<EMAIL>/zephyr?sslmode=require&channel_binding=require"
ADMIN_DB="postgresql://neondb_owner:<EMAIL>/admin?sslmode=require&channel_binding=require"
CLOUD_DB="postgresql://neondb_owner:<EMAIL>/cloud?sslmode=require&channel_binding=require"
LITELLM_DB="postgresql://neondb_owner:<EMAIL>/litellm?sslmode=require&channel_binding=require"

# 备份函数
backup_database() {
    local db_name=$1
    local db_url=$2

    echo "正在备份数据库: $db_name"

    # 使用 pg_dump 备份数据库
    pg_dump "$db_url" > "$BACKUP_DIR/${db_name}_backup.sql"

    if [ $? -eq 0 ]; then
        echo "✓ $db_name 备份成功"
    else
        echo "✗ $db_name 备份失败"
    fi

    echo "---"
}

# 依次备份所有数据库
backup_database "chat" "$CHAT_DB"
backup_database "console" "$CONSOLE_DB"
backup_database "zephyr" "$ZEPHYR_DB"
backup_database "admin" "$ADMIN_DB"
backup_database "cloud" "$CLOUD_DB"
backup_database "litellm" "$LITELLM_DB"

echo "所有数据库备份完成！"
echo "备份文件位置: $BACKUP_DIR"
ls -la "$BACKUP_DIR"
