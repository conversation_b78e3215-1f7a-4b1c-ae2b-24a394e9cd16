--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5
-- Dumped by pg_dump version 17.5 (Debian 17.5-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: drizzle; Type: SCHEMA; Schema: -; Owner: neondb_owner
--

CREATE SCHEMA drizzle;


ALTER SCHEMA drizzle OWNER TO neondb_owner;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: __drizzle_migrations; Type: TABLE; Schema: drizzle; Owner: neondb_owner
--

CREATE TABLE drizzle.__drizzle_migrations (
    id integer NOT NULL,
    hash text NOT NULL,
    created_at bigint
);


ALTER TABLE drizzle.__drizzle_migrations OWNER TO neondb_owner;

--
-- Name: __drizzle_migrations_id_seq; Type: SEQUENCE; Schema: drizzle; Owner: neondb_owner
--

CREATE SEQUENCE drizzle.__drizzle_migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE drizzle.__drizzle_migrations_id_seq OWNER TO neondb_owner;

--
-- Name: __drizzle_migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: drizzle; Owner: neondb_owner
--

ALTER SEQUENCE drizzle.__drizzle_migrations_id_seq OWNED BY drizzle.__drizzle_migrations.id;


--
-- Name: aggregated_model; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.aggregated_model (
    id character varying(150) NOT NULL,
    logo text,
    display_name character varying(200),
    description text,
    enabled boolean DEFAULT true NOT NULL,
    type character varying(20) DEFAULT 'chat'::character varying NOT NULL,
    fallback_model_id text,
    pricing jsonb NOT NULL,
    abilities jsonb DEFAULT '{}'::jsonb,
    context_window_tokens integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    accessed_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.aggregated_model OWNER TO neondb_owner;

--
-- Name: aggregated_model_litellm_model; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.aggregated_model_litellm_model (
    aggregated_model_id text NOT NULL,
    litellm_model_id text NOT NULL,
    infra_provider_id text NOT NULL,
    infra_model_id text NOT NULL,
    litellm_params jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.aggregated_model_litellm_model OWNER TO neondb_owner;

--
-- Name: models; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.models (
    id character varying(150) NOT NULL,
    display_name character varying(200),
    description text,
    enabled boolean,
    provider_id character varying(64) NOT NULL,
    type character varying(20) DEFAULT 'chat'::character varying NOT NULL,
    sort integer,
    pricing jsonb,
    parameters jsonb DEFAULT '{}'::jsonb,
    config jsonb,
    abilities jsonb DEFAULT '{}'::jsonb,
    context_window_tokens integer,
    source character varying(20),
    released_at character varying(10),
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    accessed_at timestamp without time zone DEFAULT now() NOT NULL,
    CONSTRAINT models_source_check CHECK (((source)::text = ANY (ARRAY[('remote'::character varying)::text, ('custom'::character varying)::text, ('builtin'::character varying)::text])))
);


ALTER TABLE public.models OWNER TO neondb_owner;

--
-- Name: TABLE models; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON TABLE public.models IS 'Admin 管理的 AI 模型表';


--
-- Name: COLUMN models.id; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.id IS '模型ID，主键';


--
-- Name: COLUMN models.display_name; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.display_name IS '模型显示名称';


--
-- Name: COLUMN models.description; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.description IS '模型描述';


--
-- Name: COLUMN models.enabled; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.enabled IS '是否启用';


--
-- Name: COLUMN models.provider_id; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.provider_id IS '所属提供商ID';


--
-- Name: COLUMN models.type; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.type IS '模型类型，默认为chat';


--
-- Name: COLUMN models.sort; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.sort IS '排序权重';


--
-- Name: COLUMN models.pricing; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.pricing IS '定价信息（JSON格式）';


--
-- Name: COLUMN models.parameters; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.parameters IS '模型参数（JSON格式）';


--
-- Name: COLUMN models.config; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.config IS '模型配置（JSON格式）';


--
-- Name: COLUMN models.abilities; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.abilities IS '模型能力（JSON格式）';


--
-- Name: COLUMN models.context_window_tokens; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.context_window_tokens IS '上下文窗口token数';


--
-- Name: COLUMN models.source; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.source IS '来源类型：remote-远程，custom-自定义，builtin-内置';


--
-- Name: COLUMN models.released_at; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.models.released_at IS '发布日期';


--
-- Name: providers; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.providers (
    id character varying(64) NOT NULL,
    name text,
    enabled boolean,
    logo text,
    description text,
    key_vaults text,
    source character varying(20),
    settings jsonb DEFAULT '{}'::jsonb,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    accessed_at timestamp without time zone DEFAULT now() NOT NULL,
    sort integer,
    litellm_credential text NOT NULL,
    CONSTRAINT providers_source_check CHECK (((source)::text = ANY (ARRAY[('builtin'::character varying)::text, ('custom'::character varying)::text])))
);


ALTER TABLE public.providers OWNER TO neondb_owner;

--
-- Name: TABLE providers; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON TABLE public.providers IS 'Admin 管理的 AI 提供商表';


--
-- Name: COLUMN providers.id; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.providers.id IS '提供商ID，主键';


--
-- Name: COLUMN providers.name; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.providers.name IS '提供商名称';


--
-- Name: COLUMN providers.enabled; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.providers.enabled IS '是否启用';


--
-- Name: COLUMN providers.logo; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.providers.logo IS '提供商Logo';


--
-- Name: COLUMN providers.description; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.providers.description IS '提供商描述';


--
-- Name: COLUMN providers.key_vaults; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.providers.key_vaults IS 'API密钥等敏感信息，需要加密存储';


--
-- Name: COLUMN providers.source; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.providers.source IS '来源类型：builtin-内置，custom-自定义';


--
-- Name: COLUMN providers.settings; Type: COMMENT; Schema: public; Owner: neondb_owner
--

COMMENT ON COLUMN public.providers.settings IS '提供商设置（JSON格式）';


--
-- Name: role_quotas; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.role_quotas (
    id integer NOT NULL,
    role_id text NOT NULL,
    token_budget double precision NOT NULL,
    file_mb integer NOT NULL,
    vector_count integer NOT NULL,
    accessed_at timestamp with time zone DEFAULT now() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.role_quotas OWNER TO neondb_owner;

--
-- Name: role_quota_management_id_seq; Type: SEQUENCE; Schema: public; Owner: neondb_owner
--

ALTER TABLE public.role_quotas ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME public.role_quota_management_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: user_virtual_keys; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.user_virtual_keys (
    user_id text NOT NULL,
    role_id text NOT NULL,
    key_vaults text NOT NULL
);


ALTER TABLE public.user_virtual_keys OWNER TO neondb_owner;

--
-- Name: __drizzle_migrations id; Type: DEFAULT; Schema: drizzle; Owner: neondb_owner
--

ALTER TABLE ONLY drizzle.__drizzle_migrations ALTER COLUMN id SET DEFAULT nextval('drizzle.__drizzle_migrations_id_seq'::regclass);


--
-- Data for Name: __drizzle_migrations; Type: TABLE DATA; Schema: drizzle; Owner: neondb_owner
--

COPY drizzle.__drizzle_migrations (id, hash, created_at) FROM stdin;
1	b52483bdeac539db5cc09ba4c48a46541ea36ab68e286ea5eaac9d1c1c9eb247	1752426879136
\.


--
-- Data for Name: aggregated_model; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.aggregated_model (id, logo, display_name, description, enabled, type, fallback_model_id, pricing, abilities, context_window_tokens, created_at, updated_at, accessed_at) FROM stdin;
hybrid-model	\N	混合模型	第一个混合模型	t	chat	\N	{"input": 15, "output": 50}	{"search": false, "vision": false, "reasoning": true, "imageOutput": false, "functionCall": true}	131072	2025-07-16 16:34:37.040505+00	2025-07-16 16:34:37.040505+00	2025-07-16 16:34:37.040505+00
gpt-4.1-mini	\N	挽歌模型	挽歌测试模型	t	chat	hybrid-model	{"input": 0.1, "output": 0.1}	{"search": false, "vision": false, "reasoning": false, "imageOutput": false, "functionCall": false}	130048	2025-07-19 08:16:24.894355+00	2025-07-19 08:16:24.894355+00	2025-07-19 08:16:24.894355+00
Google	\N	Google-All	\N	t	chat	\N	{"input": 1, "output": 3}	{"search": true, "vision": true, "reasoning": true, "imageOutput": false, "functionCall": true}	131072	2025-07-19 13:39:33.976206+00	2025-07-19 13:39:33.976206+00	2025-07-19 13:39:33.976206+00
google-test	\N	测试谷歌模型1		t	chat	\N	{"input": 3, "output": 9}	{"search": false, "vision": false, "reasoning": false, "imageOutput": false, "functionCall": false}	94208	2025-07-19 13:59:33.141106+00	2025-07-19 13:59:33.141106+00	2025-07-19 13:59:33.141106+00
gac-test	\N	GAC测试	\N	t	chat	google-test	{"input": 5, "output": 15}	{"search": true, "vision": true, "reasoning": true, "imageOutput": false, "functionCall": true}	\N	2025-07-27 15:00:15.319578+00	2025-07-27 15:00:15.319578+00	2025-07-27 15:00:15.319578+00
\.


--
-- Data for Name: aggregated_model_litellm_model; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.aggregated_model_litellm_model (aggregated_model_id, litellm_model_id, infra_provider_id, infra_model_id, litellm_params) FROM stdin;
gpt-4.1-mini	9b2cfad3-05bf-472a-a2ae-43fbe9ab46d8	chatanywhere	gpt-4.1-mini	{}
gpt-4.1-mini	beeb9518-f88f-43cf-a5fa-0d771207d8ef	chatanywhere	chatgpt-4o-latest	{}
google-test	84d2a21e-ef36-4618-8f3f-0cdf01d7c072	GoogleGemini	gemini/gemini-2.5-flash	{"weight": 2}
google-test	3e030c3e-c9f3-45a6-8889-e885cb73eebe	google-2	gemini/gemini-2.5-flash	{"weight": 3}
google-test	337fb0fb-4481-49ee-8b35-32503d5ed77b	GoogleGemini	gemini/gemini-2.0-flash	{"model": "gemini/gemini-2.0-flash", "weight": 100, "input_cost_per_token": 0.000003, "output_cost_per_token": 0.000009, "litellm_credential_name": "GoogleGemini_credential"}
google-test	4675b024-e350-434b-a30f-05f824487909	google-2	gemini/gemini-2.5-pro	{"weight": 1}
google-test	cd4cd5df-5126-4b9a-859c-cbccffabdaf4	GoogleGemini	gemini/gemini-2.5-pro	{"model": "gemini/gemini-2.5-pro", "weight": 1, "input_cost_per_token": 0.000003, "output_cost_per_token": 0.000009, "litellm_credential_name": "GoogleGemini_credential"}
gac-test	a30d2aed-9c62-4c53-92fe-93bacbd1ee90	gac	chatgpt-4o-latest	{"model": "chatgpt-4o-latest", "weight": 1, "input_cost_per_token": 0.000005, "output_cost_per_token": 0.000015, "litellm_credential_name": "gac_credential"}
gac-test	10312db9-6055-4945-871e-30cc184f3a37	gac	gpt-4	{"model": "gpt-4", "weight": 1, "input_cost_per_token": 0.000005, "output_cost_per_token": 0.000015, "litellm_credential_name": "gac_credential"}
gac-test	db4c8b48-c78d-4d10-9043-440fc31873d1	gac	gpt-4.1	{"model": "gpt-4.1", "weight": 1, "input_cost_per_token": 0.000005, "output_cost_per_token": 0.000015, "litellm_credential_name": "gac_credential"}
\.


--
-- Data for Name: models; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.models (id, display_name, description, enabled, provider_id, type, sort, pricing, parameters, config, abilities, context_window_tokens, source, released_at, created_at, updated_at, accessed_at) FROM stdin;
gemini/gemini-2.5-flash	Gemini 2.5 Flash	Gemini 2.5 Flash 是 Google 性价比最高的模型，提供全面的功能。	t	GoogleGemini	chat	0	{"input": 0.3, "output": 2.5}	{}	\N	{"search": true, "vision": true, "reasoning": true, "imageOutput": false, "functionCall": true, "imageGeneration": false}	1114112	custom	2025-06-17	2025-07-19 14:02:48.174986	2025-07-19 14:02:48.174986	2025-07-19 14:02:48.174986
o4-mini	o4-mini	o4-mini 是我们最新的小型 o 系列模型。 它专为快速有效的推理而优化，在编码和视觉任务中表现出极高的效率和性能。	t	chatanywhere	chat	0	{"input": 1.1, "output": 4.4, "cachedInput": 0.275}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	200000	builtin	2025-04-17	2025-07-19 08:24:13.471829	2025-07-19 08:24:13.471829	2025-07-19 08:24:13.471829
o3	o3	o3 是一款全能强大的模型，在多个领域表现出色。它为数学、科学、编程和视觉推理任务树立了新标杆。它也擅长技术写作和指令遵循。用户可利用它分析文本、代码和图像，解决多步骤的复杂问题。	t	chatanywhere	chat	0	{"input": 2, "output": 8, "cachedInput": 0.5}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	200000	builtin	2025-04-16	2025-07-19 08:24:13.471829	2025-07-19 08:24:13.471829	2025-07-19 08:24:13.471829
gpt-4.1	GPT-4.1	GPT-4.1 是我们用于复杂任务的旗舰模型。它非常适合跨领域解决问题。	t	chatanywhere	chat	0	{"input": 2, "output": 8, "cachedInput": 0.5}	{}	\N	{"search": true, "vision": true, "functionCall": true}	1047576	builtin	2025-04-14	2025-07-19 08:24:13.471829	2025-07-19 08:24:13.471829	2025-07-19 08:24:13.471829
gpt-4.1-mini	GPT-4.1 mini	GPT-4.1 mini 提供了智能、速度和成本之间的平衡，使其成为许多用例中有吸引力的模型。	t	chatanywhere	chat	0	{"input": 0.4, "output": 1.6, "cachedInput": 0.1}	{}	\N	{"search": true, "vision": true, "functionCall": true}	1047576	builtin	2025-04-14	2025-07-19 08:24:13.471829	2025-07-19 08:24:13.471829	2025-07-19 08:24:13.471829
chatgpt-4o-latest	ChatGPT-4o	ChatGPT-4o 是一款动态模型，实时更新以保持当前最新版本。它结合了强大的语言理解与生成能力，适合于大规模应用场景，包括客户服务、教育和技术支持。	t	chatanywhere	chat	0	{"input": 5, "output": 15}	{}	\N	{"vision": true}	128000	builtin	2024-08-14	2025-07-19 08:24:13.471829	2025-07-19 08:24:13.471829	2025-07-19 08:24:13.471829
gemini/gemini-2.0-flash-preview-image-generation	Gemini 2.0 Flash Preview Image Generation	Gemini 2.0 Flash 预览模型，支持图像生成	t	GoogleGemini	chat	0	{"input": 0.1, "output": 0.039}	{}	\N	{"vision": true, "imageOutput": true}	40960	builtin	2025-05-07	2025-07-19 14:02:48.174986	2025-07-19 14:02:48.174986	2025-07-19 14:02:48.174986
ce	1	\N	t	GoogleGemini	chat	\N	{"input": 0, "output": 0}	{}	\N	{"search": false, "vision": false, "reasoning": false, "imageOutput": false, "functionCall": false, "imageGeneration": false}	\N	custom	\N	2025-07-19 15:07:49.55395	2025-07-19 15:07:49.55395	2025-07-19 15:07:49.55395
gemini/gemini-2.5-pro	Gemini 2.5 Pro	\N	t	GoogleGemini	chat	\N	{"input": 3, "output": 15}	{}	\N	{"search": true, "vision": true, "reasoning": true, "imageOutput": false, "functionCall": true, "imageGeneration": false}	131072	custom	\N	2025-07-20 12:08:12.582713	2025-07-20 12:08:12.582713	2025-07-20 12:08:12.582713
gemini/gemini-2.5-pro-preview-06-05	Gemini 2.5 Pro Preview 06-05 (Paid)	Gemini 2.5 Pro Preview 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。	t	GoogleGemini	chat	0	{"input": 1.25, "output": 10}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	2025-06-05	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.5-pro-preview-05-06	Gemini 2.5 Pro Preview 05-06 (Paid)	Gemini 2.5 Pro Preview 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。	t	GoogleGemini	chat	0	{"input": 1.25, "output": 10}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	2025-05-06	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.5-pro-exp-03-25	Gemini 2.5 Pro Experimental 03-25	Gemini 2.5 Pro Experimental 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。	t	GoogleGemini	chat	0	{"input": 0, "output": 0}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	2025-03-25	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.5-flash-preview-05-20	Gemini 2.5 Flash Preview 05-20	Gemini 2.5 Flash Preview 是 Google 性价比最高的模型，提供全面的功能。	t	GoogleGemini	chat	0	{"input": 0.15, "output": 3.5}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	2025-05-20	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.5-flash-preview-04-17	Gemini 2.5 Flash Preview 04-17	Gemini 2.5 Flash Preview 是 Google 性价比最高的模型，提供全面的功能。	t	GoogleGemini	chat	0	{"input": 0.15, "output": 3.5}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	2025-04-17	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.0-flash-preview-image-generation	Gemini 2.0 Flash Preview Image Generation	Gemini 2.0 Flash 预览模型，支持图像生成1	t	google-2	chat	0	{"input": 0.1, "output": 0.039}	{}	\N	{"search": false, "vision": true, "reasoning": false, "imageOutput": false, "functionCall": false, "imageGeneration": false}	40960	custom	2025-05-07	2025-07-19 16:38:06.715314	2025-07-19 16:38:06.715314	2025-07-19 16:38:06.715314
gemini/gemini-2.5-pro	gemini-2.5-pro	\N	f	google-2	chat	\N	{"input": 10, "output": 30}	{}	\N	{"search": true, "vision": true, "reasoning": true, "imageOutput": false, "functionCall": true, "imageGeneration": false}	131072	custom	\N	2025-07-19 16:58:30.503821	2025-07-19 16:58:30.503821	2025-07-19 16:58:30.503821
gemini/gemini-2.5-flash	Gemini 2.5 Flash	Gemini 2.5 Flash 是 Google 性价比最高的模型，提供全面的功能。	t	google-2	chat	0	{"input": 0.3, "output": 2.5, "cachedInput": 0.075}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	2025-06-17	2025-07-19 16:38:06.715314	2025-07-19 16:38:06.715314	2025-07-19 16:38:06.715314
gemini/gemini-2.5-flash-preview-04-17-thinking	Gemini 2.5 Flash Preview 04-17 for cursor testing	Gemini 2.5 Flash Preview 是 Google 性价比最高的模型，提供全面的功能。	t	GoogleGemini	chat	0	{"input": 0.15, "output": 3.5}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	\N	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.5-flash-lite-preview-06-17	Gemini 2.5 Flash-Lite Preview 06-17	Gemini 2.5 Flash-Lite Preview 是 Google 最小、性价比最高的模型，专为大规模使用而设计。	t	GoogleGemini	chat	0	{"input": 0.1, "output": 0.4, "cachedInput": 0.025}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	131072	builtin	2025-06-11	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
o3-mini	o3-mini	o3-mini 是我们最新的小型推理模型，在与 o1-mini 相同的成本和延迟目标下提供高智能。	t	gac	chat	0	{"input": 1.1, "output": 4.4, "cachedInput": 0.55}	{}	\N	{"reasoning": true, "functionCall": true}	200000	builtin	2025-01-31	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gemini/gemini-2.0-flash	Gemini 2.0 Flash	Gemini 2.0 Flash 提供下一代功能和改进，包括卓越的速度、原生工具使用、多模态生成和1M令牌上下文窗口。	t	GoogleGemini	chat	0	{"input": 0.1, "output": 0.4, "cachedInput": 0.025}	{}	\N	{"search": true, "vision": true, "functionCall": true}	1056768	builtin	2025-02-05	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.0-flash-001	Gemini 2.0 Flash 001	Gemini 2.0 Flash 提供下一代功能和改进，包括卓越的速度、原生工具使用、多模态生成和1M令牌上下文窗口。	t	GoogleGemini	chat	0	{"input": 0.1, "output": 0.4, "cachedInput": 0.025}	{}	\N	{"search": true, "vision": true, "functionCall": true}	1056768	builtin	2025-02-05	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.0-flash-exp-image-generation	Gemini 2.0 Flash (Image Generation) Experimental	Gemini 2.0 Flash 实验模型，支持图像生成	t	GoogleGemini	chat	0	{"input": 0, "output": 0}	{}	\N	{"vision": true, "imageOutput": true}	1056768	builtin	2025-03-14	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.0-flash-lite	Gemini 2.0 Flash-Lite	Gemini 2.0 Flash 模型变体，针对成本效益和低延迟等目标进行了优化。	t	GoogleGemini	chat	0	{"input": 0.075, "output": 0.3, "cachedInput": 0.01875}	{}	\N	{"vision": true}	1056768	builtin	2025-02-05	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.0-flash-lite-001	Gemini 2.0 Flash-Lite 001	Gemini 2.0 Flash 模型变体，针对成本效益和低延迟等目标进行了优化。	t	GoogleGemini	chat	0	{"input": 0.075, "output": 0.3, "cachedInput": 0.01875}	{}	\N	{"vision": true}	1056768	builtin	2025-02-05	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.0-flash-exp	Gemini 2.0 Flash Exp	Gemini 2.0 Flash 模型变体，针对成本效益和低延迟等目标进行了优化。	t	GoogleGemini	chat	0	{"input": 0, "output": 0}	{}	\N	{"vision": true, "imageOutput": true}	1056768	builtin	2025-02-05	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-1.5-flash-002	Gemini 1.5 Flash 002	Gemini 1.5 Flash 002 是一款高效的多模态模型，支持广泛应用的扩展。	t	GoogleGemini	chat	0	{"input": 0.075, "output": 0.3, "cachedInput": 0.01875}	{}	\N	{"vision": true, "functionCall": true}	1008192	builtin	2024-09-25	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-1.5-flash-001	Gemini 1.5 Flash 001	Gemini 1.5 Flash 001 是一款高效的多模态模型，支持广泛应用的扩展。	t	GoogleGemini	chat	0	{"input": 0.075, "output": 0.3, "cachedInput": 0.01875}	{}	\N	{"vision": true, "functionCall": true}	1008192	builtin	\N	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-1.5-pro-002	Gemini 1.5 Pro 002 (Paid)	Gemini 1.5 Pro 002 是最新的生产就绪模型，提供更高质量的输出，特别在数学、长上下文和视觉任务方面有显著提升。	t	GoogleGemini	chat	0	{"input": 1.25, "output": 5, "cachedInput": 0.3125}	{}	\N	{"vision": true, "functionCall": true}	2008192	builtin	2024-09-24	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-1.5-pro-001	Gemini 1.5 Pro 001 (Paid)	Gemini 1.5 Pro 001 是可扩展的多模态AI解决方案，支持广泛的复杂任务。	t	GoogleGemini	chat	0	{"input": 1.25, "output": 5, "cachedInput": 0.3125}	{}	\N	{"vision": true, "functionCall": true}	2008192	builtin	2024-02-15	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-1.5-flash-8b-latest	Gemini 1.5 Flash 8B	Gemini 1.5 Flash 8B 是一款高效的多模态模型，支持广泛应用的扩展。	t	GoogleGemini	chat	0	{"input": 0.0375, "output": 0.15, "cachedInput": 0.01}	{}	\N	{"vision": true, "functionCall": true}	1008192	builtin	2024-10-03	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemma-3-1b-it	Gemma 3 1B	\N	t	GoogleGemini	chat	0	{"input": 0, "output": 0, "cachedInput": 0}	{}	\N	{}	40960	builtin	\N	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemma-3-4b-it	Gemma 3 4B	\N	t	GoogleGemini	chat	0	{"input": 0, "output": 0, "cachedInput": 0}	{}	\N	{}	40960	builtin	\N	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemma-3-12b-it	Gemma 3 12B	\N	t	GoogleGemini	chat	0	{"input": 0, "output": 0, "cachedInput": 0}	{}	\N	{}	40960	builtin	\N	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemma-3-27b-it	Gemma 3 27B	\N	t	GoogleGemini	chat	0	{"input": 0, "output": 0, "cachedInput": 0}	{}	\N	{}	139264	builtin	\N	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemma-3n-e4b-it	Gemma 3n E4B	\N	t	GoogleGemini	chat	0	{"input": 0, "output": 0, "cachedInput": 0}	{}	\N	{}	10240	builtin	\N	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629	2025-07-20 13:40:40.72629
gemini/gemini-2.5-pro-preview-06-05	Gemini 2.5 Pro Preview 06-05 (Paid)	Gemini 2.5 Pro Preview 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。	t	google-2	chat	0	{"input": 1.25, "output": 10}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	2025-06-05	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.5-pro-preview-05-06	Gemini 2.5 Pro Preview 05-06 (Paid)	Gemini 2.5 Pro Preview 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。	t	google-2	chat	0	{"input": 1.25, "output": 10}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	2025-05-06	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.5-pro-exp-03-25	Gemini 2.5 Pro Experimental 03-25	Gemini 2.5 Pro Experimental 是 Google 最先进的思维模型，能够对代码、数学和STEM领域的复杂问题进行推理，以及使用长上下文分析大型数据集、代码库和文档。	t	google-2	chat	0	{"input": 0, "output": 0}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	2025-03-25	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.5-flash-preview-05-20	Gemini 2.5 Flash Preview 05-20	Gemini 2.5 Flash Preview 是 Google 性价比最高的模型，提供全面的功能。	t	google-2	chat	0	{"input": 0.15, "output": 3.5}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	2025-05-20	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.5-flash-preview-04-17	Gemini 2.5 Flash Preview 04-17	Gemini 2.5 Flash Preview 是 Google 性价比最高的模型，提供全面的功能。	t	google-2	chat	0	{"input": 0.15, "output": 3.5}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	2025-04-17	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.5-flash-preview-04-17-thinking	Gemini 2.5 Flash Preview 04-17 for cursor testing	Gemini 2.5 Flash Preview 是 Google 性价比最高的模型，提供全面的功能。	t	google-2	chat	0	{"input": 0.15, "output": 3.5}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	1114112	builtin	\N	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.5-flash-lite-preview-06-17	Gemini 2.5 Flash-Lite Preview 06-17	Gemini 2.5 Flash-Lite Preview 是 Google 最小、性价比最高的模型，专为大规模使用而设计。	t	google-2	chat	0	{"input": 0.1, "output": 0.4, "cachedInput": 0.025}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	131072	builtin	2025-06-11	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.0-flash	Gemini 2.0 Flash	Gemini 2.0 Flash 提供下一代功能和改进，包括卓越的速度、原生工具使用、多模态生成和1M令牌上下文窗口。	t	google-2	chat	0	{"input": 0.1, "output": 0.4, "cachedInput": 0.025}	{}	\N	{"search": true, "vision": true, "functionCall": true}	1056768	builtin	2025-02-05	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.0-flash-001	Gemini 2.0 Flash 001	Gemini 2.0 Flash 提供下一代功能和改进，包括卓越的速度、原生工具使用、多模态生成和1M令牌上下文窗口。	t	google-2	chat	0	{"input": 0.1, "output": 0.4, "cachedInput": 0.025}	{}	\N	{"search": true, "vision": true, "functionCall": true}	1056768	builtin	2025-02-05	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.0-flash-exp-image-generation	Gemini 2.0 Flash (Image Generation) Experimental	Gemini 2.0 Flash 实验模型，支持图像生成	t	google-2	chat	0	{"input": 0, "output": 0}	{}	\N	{"vision": true, "imageOutput": true}	1056768	builtin	2025-03-14	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.0-flash-lite	Gemini 2.0 Flash-Lite	Gemini 2.0 Flash 模型变体，针对成本效益和低延迟等目标进行了优化。	t	google-2	chat	0	{"input": 0.075, "output": 0.3, "cachedInput": 0.01875}	{}	\N	{"vision": true}	1056768	builtin	2025-02-05	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.0-flash-lite-001	Gemini 2.0 Flash-Lite 001	Gemini 2.0 Flash 模型变体，针对成本效益和低延迟等目标进行了优化。	t	google-2	chat	0	{"input": 0.075, "output": 0.3, "cachedInput": 0.01875}	{}	\N	{"vision": true}	1056768	builtin	2025-02-05	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-2.0-flash-exp	Gemini 2.0 Flash Exp	Gemini 2.0 Flash 模型变体，针对成本效益和低延迟等目标进行了优化。	t	google-2	chat	0	{"input": 0, "output": 0}	{}	\N	{"vision": true, "imageOutput": true}	1056768	builtin	2025-02-05	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-1.5-flash-002	Gemini 1.5 Flash 002	Gemini 1.5 Flash 002 是一款高效的多模态模型，支持广泛应用的扩展。	t	google-2	chat	0	{"input": 0.075, "output": 0.3, "cachedInput": 0.01875}	{}	\N	{"vision": true, "functionCall": true}	1008192	builtin	2024-09-25	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-1.5-flash-001	Gemini 1.5 Flash 001	Gemini 1.5 Flash 001 是一款高效的多模态模型，支持广泛应用的扩展。	t	google-2	chat	0	{"input": 0.075, "output": 0.3, "cachedInput": 0.01875}	{}	\N	{"vision": true, "functionCall": true}	1008192	builtin	\N	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-1.5-pro-002	Gemini 1.5 Pro 002 (Paid)	Gemini 1.5 Pro 002 是最新的生产就绪模型，提供更高质量的输出，特别在数学、长上下文和视觉任务方面有显著提升。	t	google-2	chat	0	{"input": 1.25, "output": 5, "cachedInput": 0.3125}	{}	\N	{"vision": true, "functionCall": true}	2008192	builtin	2024-09-24	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-1.5-pro-001	Gemini 1.5 Pro 001 (Paid)	Gemini 1.5 Pro 001 是可扩展的多模态AI解决方案，支持广泛的复杂任务。	t	google-2	chat	0	{"input": 1.25, "output": 5, "cachedInput": 0.3125}	{}	\N	{"vision": true, "functionCall": true}	2008192	builtin	2024-02-15	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemini-1.5-flash-8b-latest	Gemini 1.5 Flash 8B	Gemini 1.5 Flash 8B 是一款高效的多模态模型，支持广泛应用的扩展。	t	google-2	chat	0	{"input": 0.0375, "output": 0.15, "cachedInput": 0.01}	{}	\N	{"vision": true, "functionCall": true}	1008192	builtin	2024-10-03	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemma-3-1b-it	Gemma 3 1B	\N	t	google-2	chat	0	{"input": 0, "output": 0, "cachedInput": 0}	{}	\N	{}	40960	builtin	\N	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemma-3-4b-it	Gemma 3 4B	\N	t	google-2	chat	0	{"input": 0, "output": 0, "cachedInput": 0}	{}	\N	{}	40960	builtin	\N	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemma-3-12b-it	Gemma 3 12B	\N	t	google-2	chat	0	{"input": 0, "output": 0, "cachedInput": 0}	{}	\N	{}	40960	builtin	\N	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemma-3-27b-it	Gemma 3 27B	\N	t	google-2	chat	0	{"input": 0, "output": 0, "cachedInput": 0}	{}	\N	{}	139264	builtin	\N	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
gemini/gemma-3n-e4b-it	Gemma 3n E4B	\N	t	google-2	chat	0	{"input": 0, "output": 0, "cachedInput": 0}	{}	\N	{}	10240	builtin	\N	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974	2025-07-23 13:29:19.072974
o4-mini	o4-mini	o4-mini 是我们最新的小型 o 系列模型。 它专为快速有效的推理而优化，在编码和视觉任务中表现出极高的效率和性能。	t	gac	chat	0	{"input": 1.1, "output": 4.4, "cachedInput": 0.275}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	200000	builtin	2025-04-17	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
o4-mini-deep-research	o4-mini Deep Research	o4-mini-deep-research 是我们更快速、更实惠的深度研究模型——非常适合处理复杂的多步骤研究任务。它可以从互联网上搜索和综合信息，也可以通过 MCP 连接器访问并利用你的自有数据。	t	gac	chat	0	{"input": 2, "output": 8, "cachedInput": 0.5}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	200000	builtin	2025-06-26	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
o3-pro	o3-pro	o3-pro 模型使用更多的计算来更深入地思考并始终提供更好的答案，仅支持 Responses API 下使用。	t	gac	chat	0	{"input": 20, "output": 80}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	200000	builtin	2025-06-10	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
o3	o3	o3 是一款全能强大的模型，在多个领域表现出色。它为数学、科学、编程和视觉推理任务树立了新标杆。它也擅长技术写作和指令遵循。用户可利用它分析文本、代码和图像，解决多步骤的复杂问题。	t	gac	chat	0	{"input": 2, "output": 8, "cachedInput": 0.5}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	200000	builtin	2025-04-16	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
o3-deep-research	o3 Deep Research	o3-deep-research 是我们最先进的深度研究模型，专为处理复杂的多步骤研究任务而设计。它可以从互联网上搜索和综合信息，也可以通过 MCP 连接器访问并利用你的自有数据。	t	gac	chat	0	{"input": 10, "output": 40, "cachedInput": 2.5}	{}	\N	{"search": true, "vision": true, "reasoning": true, "functionCall": true}	200000	builtin	2025-06-26	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
o1-pro	o1-pro	o1 系列模型经过强化学习训练，能够在回答前进行思考，并执行复杂的推理任务。o1-pro 模型使用了更多计算资源，以进行更深入的思考，从而持续提供更优质的回答。	t	gac	chat	0	{"input": 150, "output": 600}	{}	\N	{"vision": true, "reasoning": true, "functionCall": true}	200000	builtin	2025-03-19	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
o1-mini	o1-mini	o1-mini是一款针对编程、数学和科学应用场景而设计的快速、经济高效的推理模型。该模型具有128K上下文和2023年10月的知识截止日期。	t	gac	chat	0	{"input": 1.1, "output": 4.4, "cachedInput": 0.55}	{}	\N	{"reasoning": true}	128000	builtin	2024-09-12	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
o1	o1	o1是OpenAI新的推理模型，支持图文输入并输出文本，适用于需要广泛通用知识的复杂任务。该模型具有200K上下文和2023年10月的知识截止日期。	t	gac	chat	0	{"input": 15, "output": 60, "cachedInput": 7.5}	{}	\N	{"vision": true, "reasoning": true, "functionCall": true}	200000	builtin	2024-12-17	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
o1-preview	o1-preview	o1是OpenAI新的推理模型，适用于需要广泛通用知识的复杂任务。该模型具有128K上下文和2023年10月的知识截止日期。	t	gac	chat	0	{"input": 15, "output": 60}	{}	\N	{"reasoning": true}	128000	builtin	2024-09-12	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4.1	GPT-4.1	GPT-4.1 是我们用于复杂任务的旗舰模型。它非常适合跨领域解决问题。	t	gac	chat	0	{"input": 2, "output": 8, "cachedInput": 0.5}	{}	\N	{"search": true, "vision": true, "functionCall": true}	1047576	builtin	2025-04-14	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4.1-mini	GPT-4.1 mini	GPT-4.1 mini 提供了智能、速度和成本之间的平衡，使其成为许多用例中有吸引力的模型。	t	gac	chat	0	{"input": 0.4, "output": 1.6, "cachedInput": 0.1}	{}	\N	{"search": true, "vision": true, "functionCall": true}	1047576	builtin	2025-04-14	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4.5-preview	GPT-4.5 Preview	GPT-4.5 的研究预览版，它是我们迄今为止最大、最强大的 GPT 模型。它拥有广泛的世界知识，并能更好地理解用户意图，使其在创造性任务和自主规划方面表现出色。GPT-4.5 可接受文本和图像输入，并生成文本输出（包括结构化输出）。支持关键的开发者功能，如函数调用、批量 API 和流式输出。在需要创造性、开放式思考和对话的任务（如写作、学习或探索新想法）中，GPT-4.5 表现尤为出色。知识截止日期为 2023 年 10 月。	t	gac	chat	0	{"input": 75, "output": 150, "cachedInput": 37.5}	{}	\N	{"vision": true, "functionCall": true}	128000	builtin	2025-02-27	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-mini	GPT-4o mini	GPT-4o mini是OpenAI在GPT-4 Omni之后推出的最新模型，支持图文输入并输出文本。作为他们最先进的小型模型，它比其他近期的前沿模型便宜很多，并且比GPT-3.5 Turbo便宜超过60%。它保持了最先进的智能，同时具有显著的性价比。GPT-4o mini在MMLU测试中获得了 82% 的得分，目前在聊天偏好上排名高于 GPT-4。	t	gac	chat	0	{"input": 0.15, "output": 0.6, "cachedInput": 0.075}	{}	\N	{"search": true, "vision": true, "functionCall": true}	128000	builtin	2024-07-18	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-mini-search-preview	GPT-4o mini Search Preview	GPT-4o mini 搜索预览版是一个专门训练用于理解和执行网页搜索查询的模型，使用的是 Chat Completions API。除了令牌费用之外，网页搜索查询还会按每次工具调用收取费用。	t	gac	chat	0	{"input": 0.15, "output": 0.6}	{}	\N	{"search": true}	128000	builtin	2025-03-11	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-mini-audio-preview	GPT-4o mini Audio	GPT-4o mini Audio 模型，支持音频输入输出	t	gac	chat	0	{"input": 0.15, "output": 0.6}	{}	\N	{"functionCall": true}	128000	builtin	2024-12-17	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o	GPT-4o	ChatGPT-4o 是一款动态模型，实时更新以保持当前最新版本。它结合了强大的语言理解与生成能力，适合于大规模应用场景，包括客户服务、教育和技术支持。	t	gac	chat	0	{"input": 2.5, "output": 10, "cachedInput": 1.25}	{}	\N	{"search": true, "vision": true, "functionCall": true}	128000	builtin	2024-05-13	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-search-preview	GPT-4o Search Preview	GPT-4o 搜索预览版是一个专门训练用于理解和执行网页搜索查询的模型，使用的是 Chat Completions API。除了令牌费用之外，网页搜索查询还会按每次工具调用收取费用。	t	gac	chat	0	{"input": 2.5, "output": 10}	{}	\N	{"search": true}	128000	builtin	2025-03-11	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-2024-11-20	GPT-4o 1120	ChatGPT-4o 是一款动态模型，实时更新以保持当前最新版本。它结合了强大的语言理解与生成能力，适合于大规模应用场景，包括客户服务、教育和技术支持。	t	gac	chat	0	{"input": 2.5, "output": 10, "cachedInput": 1.25}	{}	\N	{"search": true, "vision": true, "functionCall": true}	128000	builtin	2024-11-20	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-audio-preview	GPT-4o Audio	GPT-4o Audio 模型，支持音频输入输出	t	gac	chat	0	{"input": 2.5, "output": 10}	{}	\N	{"functionCall": true}	128000	builtin	2024-10-01	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
chatgpt-4o-latest	ChatGPT-4o	ChatGPT-4o 是一款动态模型，实时更新以保持当前最新版本。它结合了强大的语言理解与生成能力，适合于大规模应用场景，包括客户服务、教育和技术支持。	t	gac	chat	0	{"input": 5, "output": 15}	{}	\N	{"vision": true}	128000	builtin	2024-08-14	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4-turbo	GPT-4 Turbo	最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。	t	gac	chat	0	{"input": 10, "output": 30}	{}	\N	{"vision": true, "functionCall": true}	128000	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-2024-05-13	GPT-4o 0513	ChatGPT-4o 是一款动态模型，实时更新以保持当前最新版本。它结合了强大的语言理解与生成能力，适合于大规模应用场景，包括客户服务、教育和技术支持。	f	gac	chat	0	{"input": 5, "output": 15}	{}	\N	{"search": true, "vision": true, "functionCall": true}	128000	builtin	2024-05-13	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4-turbo-2024-04-09	GPT-4 Turbo Vision 0409	最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。	t	gac	chat	0	{"input": 10, "output": 30}	{}	\N	{"vision": true, "functionCall": true}	128000	builtin	2024-04-09	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4-turbo-preview	GPT-4 Turbo Preview	最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。	t	gac	chat	0	{"input": 10, "output": 30}	{}	\N	{"functionCall": true}	128000	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4-0125-preview	GPT-4 Turbo Preview 0125	最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。	t	gac	chat	0	{"input": 10, "output": 30}	{}	\N	{"functionCall": true}	128000	builtin	2024-01-25	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4-1106-preview	GPT-4 Turbo Preview 1106	最新的 GPT-4 Turbo 模型具备视觉功能。现在，视觉请求可以使用 JSON 模式和函数调用。 GPT-4 Turbo 是一个增强版本，为多模态任务提供成本效益高的支持。它在准确性和效率之间找到平衡，适合需要进行实时交互的应用程序场景。	t	gac	chat	0	{"input": 10, "output": 30}	{}	\N	{"functionCall": true}	128000	builtin	2023-11-06	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4	GPT-4	GPT-4 提供了一个更大的上下文窗口，能够处理更长的文本输入，适用于需要广泛信息整合和数据分析的场景。	t	gac	chat	0	{"input": 30, "output": 60}	{}	\N	{"functionCall": true}	8192	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4-0613	GPT-4 0613	GPT-4 提供了一个更大的上下文窗口，能够处理更长的文本输入，适用于需要广泛信息整合和数据分析的场景。	t	gac	chat	0	{"input": 30, "output": 60}	{}	\N	{"functionCall": true}	8192	builtin	2023-06-13	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4-32k	GPT-4 32K	GPT-4 提供了一个更大的上下文窗口，能够处理更长的文本输入，适用于需要广泛信息整合和数据分析的场景。	t	gac	chat	0	{"input": 60, "output": 120}	{}	\N	{"functionCall": true}	32768	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-3.5-turbo	GPT-3.5 Turbo	GPT 3.5 Turbo，适用于各种文本生成和理解任务，Currently points to gpt-3.5-turbo-0125	t	gac	chat	0	{"input": 0.5, "output": 1.5}	{}	\N	{"functionCall": true}	16384	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-3.5-turbo-0125	GPT-3.5 Turbo 0125	GPT 3.5 Turbo，适用于各种文本生成和理解任务，Currently points to gpt-3.5-turbo-0125	t	gac	chat	0	{"input": 0.5, "output": 1.5}	{}	\N	{"functionCall": true}	16384	builtin	2024-01-25	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-3.5-turbo-1106	GPT-3.5 Turbo 1106	GPT 3.5 Turbo，适用于各种文本生成和理解任务，Currently points to gpt-3.5-turbo-0125	t	gac	chat	0	{"input": 1, "output": 2}	{}	\N	{"functionCall": true}	16384	builtin	2023-11-06	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-3.5-turbo-instruct	GPT-3.5 Turbo Instruct	GPT 3.5 Turbo，适用于各种文本生成和理解任务，对指令遵循的优化	t	gac	chat	0	{"input": 1.5, "output": 2}	{}	\N	{}	4096	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
codex-mini-latest	Codex mini	codex-mini-latest 是 o4-mini 的微调版本，专门用于 Codex CLI。对于直接通过 API 使用，我们推荐从 gpt-4.1 开始。	t	gac	chat	0	{"input": 1.5, "output": 6}	{}	\N	{"vision": true, "reasoning": true, "functionCall": true}	200000	builtin	2025-06-01	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
computer-use-preview	Computer Use Preview	computer-use-preview 模型是专为“计算机使用工具”设计的专用模型，经过训练以理解并执行计算机相关任务。	t	gac	chat	0	{"input": 3, "output": 12}	{}	\N	{"vision": true, "reasoning": true, "functionCall": true}	8192	builtin	2025-03-11	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
text-embedding-3-large	Text Embedding 3 Large	最强大的向量化模型，适用于英文和非英文任务	t	gac	embedding	0	{"input": 0.13, "currency": "USD"}	{}	\N	{}	8192	builtin	2024-01-25	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
text-embedding-3-small	Text Embedding 3 Small	高效且经济的新一代 Embedding 模型，适用于知识检索、RAG 应用等场景	t	gac	embedding	0	{"input": 0.02, "currency": "USD"}	{}	\N	{}	8192	builtin	2024-01-25	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
tts-1	TTS-1	最新的文本转语音模型，针对实时场景优化速度	t	gac	tts	0	{"input": 15}	{}	\N	{}	\N	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
tts-1-hd	TTS-1 HD	最新的文本转语音模型，针对质量进行优化	t	gac	tts	0	{"input": 30}	{}	\N	{}	\N	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-mini-tts	GPT-4o Mini TTS	GPT-4o mini TTS 是一个基于 GPT-4o mini 构建的文本转语音模型，这是一种快速且强大的语言模型。使用它可以将文本转换为自然听起来的语音文本。最大输入标记数为 2000。	t	gac	tts	0	{"input": 10}	{}	\N	{}	\N	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
whisper-1	Whisper	通用语音识别模型，支持多语言语音识别、语音翻译和语言识别	t	gac	stt	0	{"input": 0.006}	{}	\N	{}	\N	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
dall-e-3	DALL·E 3	最新的 DALL·E 模型，于2023年11月发布。支持更真实、准确的图像生成，具有更强的细节表现力	t	gac	image	0	{"hd": 0.08, "standard": 0.04}	{}	\N	{}	\N	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
dall-e-2	DALL·E 2	第二代 DALL·E 模型，支持更真实、准确的图像生成，分辨率是第一代的4倍	t	gac	image	0	{"input": 0.02}	{}	\N	{}	\N	builtin	\N	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-realtime-preview	GPT-4o Realtime	GPT-4o 实时版本，支持音频和文本实时输入输出	t	gac	realtime	0	{"input": 5, "output": 20, "audioInput": 100, "audioOutput": 200, "cachedInput": 2.5, "cachedAudioInput": 20}	{}	\N	{}	128000	builtin	2024-10-01	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-realtime-preview-2024-10-01	GPT-4o Realtime 10-01	GPT-4o 实时版本，支持音频和文本实时输入输出	t	gac	realtime	0	{"input": 5, "output": 20, "audioInput": 100, "audioOutput": 200, "cachedInput": 2.5, "cachedAudioInput": 20}	{}	\N	{}	128000	builtin	2024-10-01	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-realtime-preview-2024-12-17	GPT-4o Realtime 12-17	GPT-4o 实时版本，支持音频和文本实时输入输出	t	gac	realtime	0	{"input": 5, "output": 20, "audioInput": 40, "audioOutput": 80, "cachedInput": 2.5, "cachedAudioInput": 2.5}	{}	\N	{}	128000	builtin	2024-12-17	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4o-mini-realtime-preview	GPT-4o Mini Realtime	GPT-4o-mini 实时版本，支持音频和文本实时输入输出	t	gac	realtime	0	{"input": 0.6, "output": 2.4, "audioInput": 10, "audioOutput": 20, "cachedInput": 0.3, "cachedAudioInput": 0.3}	{}	\N	{}	128000	builtin	2024-12-17	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
gpt-4.1-nano	GPT-4.1 nano	GPT-4.1 nano 是最快，最具成本效益的GPT-4.1模型。	f	gac	chat	0	{"input": 0.1, "output": 0.4, "cachedInput": 0.025}	{}	\N	{"vision": true, "functionCall": true}	1047576	builtin	2025-04-14	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937	2025-07-27 14:59:23.639937
\.


--
-- Data for Name: providers; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.providers (id, name, enabled, logo, description, key_vaults, source, settings, created_at, updated_at, accessed_at, sort, litellm_credential) FROM stdin;
GoogleGemini	Google	t	\N	\N	4a54a280a767a3ffb198d918:e0253012d4d0e0376bf290c42228ec4d:9ebbfa7189e7f99bd721892ccdb5baba2c07e4902d3651fd1c5546a8886646ab224fd9f281fcb421c71bfe1cc03639664bf8667f3f06a2b8b7ffe9b38680a4c1fa	custom	{"sdkType": "gemini", "showModelFetcher": false, "supportResponsesApi": false}	2025-07-19 13:29:17.569493	2025-07-19 13:29:17.569493	2025-07-19 13:29:17.569493	\N	GoogleGemini_credential
chatanywhere	影和自用渠道	t	\N	\N	be58cdbb93507b674dbdba20:f58de0f830d9272ddcb84db1b2e34162:0ac8097e5ad331060851154d56911b68890f88cd6eb9900f37929a7034a1518f710c87bc848dc50453cfacda8bd05f4099bfd28fb2438dc236305b89e496f0009ccbac8ec40cf0e0956a907301dd15590ce4d1efd645a90c884f39a3eb0ae459cd43b08334045ba3b7c33e	custom	{"sdkType": "openai", "showModelFetcher": false, "supportResponsesApi": false}	2025-07-18 17:14:36.220391	2025-07-18 17:14:36.220391	2025-07-18 17:14:36.220391	\N	chatanywhere_credential
google-2	谷歌渠道二	t	\N	\N	f569917da78f1520db93536c:2667e7fec1ac304fcd7b9b7ccd68040a:86987be40f7ccd9c92de949121f4d18e30fd4090f2a0c6aebf33223604e7659477c1afea50fa716dd23c566de7f72b79045ba792	custom	{"sdkType": "gemini", "showModelFetcher": false, "supportResponsesApi": false}	2025-07-19 16:38:01.272067	2025-07-19 16:38:01.272067	2025-07-19 16:38:01.272067	\N	google-2_credential
ceshi2	测试渠道3	t	\N	gmini2	e8d850dc16a29a0447d35b2a:c479b2b46f498886d6bee010f4e3f2bb:d994de740531962ee67171e85f179b72138f0fe3681e70408553c4553a95	custom	{"sdkType": "gemini", "showModelFetcher": false, "supportResponsesApi": false}	2025-07-23 13:48:09.196929	2025-07-23 13:48:09.196929	2025-07-23 13:48:09.196929	\N	ceshi2_credential
gac	gpt-and-claude	t	\N	\N	5fdc15603ca54cabceb33163:77c9b0b2aa3109dc0884656ea1b890a3:d43aefb1b624b772f5d59d40d23185bc7256519abbb2	custom	{"sdkType": "openai", "showModelFetcher": false, "supportResponsesApi": false}	2025-07-27 14:59:10.243168	2025-07-27 14:59:10.243168	2025-07-27 14:59:10.243168	\N	gac_credential
\.


--
-- Data for Name: role_quotas; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.role_quotas (id, role_id, token_budget, file_mb, vector_count, accessed_at, created_at, updated_at) FROM stdin;
1	1	10002	1003	10000	2025-07-13 17:23:55.128361+00	2025-07-13 17:23:55.128361+00	2025-07-13 17:23:55.128361+00
6	7	1000	100	10000	2025-07-14 16:35:40.30392+00	2025-07-14 16:35:40.30392+00	2025-07-14 16:35:40.30392+00
8	9	1000	1000	10	2025-07-20 12:03:50.922585+00	2025-07-20 12:03:50.922585+00	2025-07-20 12:03:50.922585+00
7	8	1	10	10	2025-07-19 09:58:42.269802+00	2025-07-19 09:58:42.269802+00	2025-07-20 12:08:37.967+00
10	11	1	2	3	2025-07-22 12:45:01.47074+00	2025-07-22 12:45:01.47074+00	2025-07-22 12:45:01.47074+00
\.


--
-- Data for Name: user_virtual_keys; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.user_virtual_keys (user_id, role_id, key_vaults) FROM stdin;
user_2zPkr24RVhfFb3ZAYXj1PD3r1Uq	7	32c676201a5d0e74f28bd4ab:fcd10f072bde131b42cf1510942e2f8a:b7edd22dcf72153fd8dc19f601ef82d0fb64bb064d2efbf36bc538962f9ad5d2b97304e4be41df9338b6466bccc5d243a8339e15729fb874868fbd32b8ca6127c20a40dd1a45ceee357f6910a341fb10bef27daa7c39b8242d879bf94a3baef9f371d3aa4754259fb96b7979c1fe89bcf2b4cc4add96a6e113ffcec5b0e9
7ea6c63b-f336-419b-a8c7-293c59d72414	7	f3eb91b8d75cd06cb2c6de5c:13e28e92e89c5f358c5f03970ad47dc9:989c149f82076b1b2ff8006298bb84ff23eb89cafc02683b5d616f31cafec48d211485ee4effc90fadccd744253d95bd28935028e4988b06ccc2139e0e01b25a6dcbfc5ec63780677599d5d5b19d48dd0008857c2aaf72df9b2cf8887053c43dfc1bf57acbfc59862fc9291e651277a530e72428858c74543435138dfb9f
user_2zK8vhFbGNsaEFuXkSi2nav1vYW	7	cbe15ee2e80d4b1d876b8ace:51a3a35fac8fbc922d0252831ec49eef:
user_2zJyJ65ohnSwmZ5QRVRIPkTn0N2	7	e567726b59afcf669e238577:7c051b7fa627cd8b18dec85292b1dbb3:d66ff742456d16f5f9a5951da03de7d39ff78cad406942ccfa716839d07493744d2c715bb35946650c87fcb4f5c234ef878841e84a1f79b4b568a7bbff1948b401bfdc3c06c42c7e0673922d5e593a80df7fc9acced007edc64b422d126c471e25edb2c25402a3ac2c14de6fe2ad95d17ba9a2ac9c4d991f84b0658b
9e131e40-87c6-4bdc-9bb8-e619ffcd1e3b	8	0b51deee10fd6914ca038eab:ddebf81acb6712090f49b5f1e72b2519:ff9eb469453894da566894415bb9f99d7d14cf3073111c5e896b023acb4b2be72eb1380ce04116e191f044f171caa66fa1768b12d32a1f7137c57404ba5e7b03557eabf91b9d09126eeaf522b93ba9d4acfdeb4a9781cb7838ba9976d62285d2f3c9dfc362cb662f72a58219169a0e5df55fce4b6de9368d9a8863ef
7ea6c63b-f336-419b-a8c7-293c59d72414	9	8afb1ac0944732d5532e78c5:fa387def7050dd5f762764ebc0272572:25f71d81e91b8358f03bd7b2bbc5971af8b79c4892641b31cb191b5b55ecf8922a6767e71e3fefa584aaa0df728a8a5e6294228294673aa9c41080cacc638ff11e73b375c8164998d86fb8aee51bb3ab776ec9edf754f0544125500f53914280711ddaee2ec53131b7f16a29b3d4490311a929b5337e24685db448a8
7ea6c63b-f336-419b-a8c7-293c59d72414	1	bb6686ed71b8ce718070c210:7e1982a67c5cce67d934ee0c7079f4ae:2c57803c9eb1ad4f23aa0bb650f91d0d883ee99e2a70b31f7ae9f325c8d702a03195d9c71893e84c589cc386ef4164bd505c1aefbb499497566beeb8adef982c45724829c87fa6ea879e99f10e9840820c0ad8bd0bc23e0c01d7b6d7d695d9fc76dedf8441343977a02e1ac1300c2302acf3d204d679b59dcfe1ef77
9e131e40-87c6-4bdc-9bb8-e619ffcd1e3b	10	9854522eadf20cbbb37d09f5:29e9743fc0a472da6b46631696bf2984:843d68c77adc4c0cf347f17f162ec373d333673b38786b32cfd05348120c2f46a93645cb6f578ca5f7576712ce3df6af9150dd4b7ef577f331e6d8fbd3e01ae94fdc0ead65a51d55166fdef1460219a20722117dfb70790106d0ea27c1ad658f2a9d409d332ca6cc212ad08d95902bb5eddff8dcd713fb9b1ac7055f
user_2yK4sKFfa6XmNYCpnNkUXURuuIr	1	f3eb91b8d75cd06cb2c6de5c:13e28e92e89c5f358c5f03970ad47dc9:989c149f82076b1b2ff8006298bb84ff23eb89cafc02683b5d616f31cafec48d211485ee4effc90fadccd744253d95bd28935028e4988b06ccc2139e0e01b25a6dcbfc5ec63780677599d5d5b19d48dd0008857c2aaf72df9b2cf8887053c43dfc1bf57acbfc59862fc9291e651277a530e72428858c74543435138dfb9f
user_2xueIVE0lUE64xiFlVN3NdgYY3B	1	31bcad335fa6285252aaa90e:3f7cf47500ed76ea58f4a6f5d3b4697e:b8fa13efa8d06f9bb71e8f4f416836e41ee578f22dd7f8fecb606fb9c68fb8657f6d6cac921725f8b91de4fb5fd12340081238533942fab9d0ba6a6b09374bad97e7a214d97e513654ed5d438926544cee739dff5c88ed70224f2d00617be1251b5d2bfc2f6246c74c45f8e29dbc96a08c63846c36c65fa29de14dde
9e131e40-87c6-4bdc-9bb8-e619ffcd1e3b	1	aa06a7e41074f0b5d4933405:d5c4fb98cd3f01901d5b50a69d8fc181:7b56826658c5652a98ae1e8e607bcc7cf92565622851ff3b96ea115afc80ab91a26bf0c1481fe17bb9b5353accb2b6725069f6ca25a7b2a7fa31e37eec2601fa0eea8a9ee152d658b822e2b337f717f6d4cba7f60f783be25da73ae378dc2ddf3bd4e000f8796d8af94ada1795d81693e8f54c2b049164fb4829a245
\.


--
-- Name: __drizzle_migrations_id_seq; Type: SEQUENCE SET; Schema: drizzle; Owner: neondb_owner
--

SELECT pg_catalog.setval('drizzle.__drizzle_migrations_id_seq', 1, true);


--
-- Name: role_quota_management_id_seq; Type: SEQUENCE SET; Schema: public; Owner: neondb_owner
--

SELECT pg_catalog.setval('public.role_quota_management_id_seq', 17, true);


--
-- Name: __drizzle_migrations __drizzle_migrations_pkey; Type: CONSTRAINT; Schema: drizzle; Owner: neondb_owner
--

ALTER TABLE ONLY drizzle.__drizzle_migrations
    ADD CONSTRAINT __drizzle_migrations_pkey PRIMARY KEY (id);


--
-- Name: aggregated_model aggregated_model_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.aggregated_model
    ADD CONSTRAINT aggregated_model_pkey PRIMARY KEY (id);


--
-- Name: providers providers_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.providers
    ADD CONSTRAINT providers_pkey PRIMARY KEY (id);


--
-- Name: role_quotas role_quota_management_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.role_quotas
    ADD CONSTRAINT role_quota_management_pkey PRIMARY KEY (id);


--
-- Name: role_quotas role_quota_management_role_id_unique; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.role_quotas
    ADD CONSTRAINT role_quota_management_role_id_unique UNIQUE (role_id);


--
-- Name: idx_models_enabled; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX idx_models_enabled ON public.models USING btree (enabled);


--
-- Name: idx_models_provider_enabled; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX idx_models_provider_enabled ON public.models USING btree (provider_id, enabled);


--
-- Name: idx_models_source; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX idx_models_source ON public.models USING btree (source);


--
-- Name: idx_models_type; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX idx_models_type ON public.models USING btree (type);


--
-- Name: idx_providers_enabled; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX idx_providers_enabled ON public.providers USING btree (enabled);


--
-- Name: idx_providers_source; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX idx_providers_source ON public.providers USING btree (source);


--
-- Name: uq_aggregated_model_id_infra_provider_id_infra_model_id; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE UNIQUE INDEX uq_aggregated_model_id_infra_provider_id_infra_model_id ON public.aggregated_model_litellm_model USING btree (aggregated_model_id, infra_provider_id, infra_model_id);


--
-- Name: uq_aggregated_model_id_litellm_model_id; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX uq_aggregated_model_id_litellm_model_id ON public.aggregated_model_litellm_model USING btree (aggregated_model_id, litellm_model_id);


--
-- Name: uq_model_id; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX uq_model_id ON public.aggregated_model USING btree (id);


--
-- Name: uq_provider_id_id; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE UNIQUE INDEX uq_provider_id_id ON public.models USING btree (provider_id, id);


--
-- Name: uq_user_id_role_id; Type: INDEX; Schema: public; Owner: neondb_owner
--

CREATE INDEX uq_user_id_role_id ON public.user_virtual_keys USING btree (user_id, role_id);


--
-- Name: aggregated_model_litellm_model fk_aggregated_model_litellm_model_aggregated_model_id; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.aggregated_model_litellm_model
    ADD CONSTRAINT fk_aggregated_model_litellm_model_aggregated_model_id FOREIGN KEY (aggregated_model_id) REFERENCES public.aggregated_model(id) ON DELETE CASCADE;


--
-- Name: aggregated_model_litellm_model fk_aggregated_model_litellm_model_infra_provider_id; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.aggregated_model_litellm_model
    ADD CONSTRAINT fk_aggregated_model_litellm_model_infra_provider_id FOREIGN KEY (infra_provider_id) REFERENCES public.providers(id) ON DELETE CASCADE;


--
-- Name: models fk_models_provider_id; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.models
    ADD CONSTRAINT fk_models_provider_id FOREIGN KEY (provider_id) REFERENCES public.providers(id) ON DELETE CASCADE;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: cloud_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE cloud_admin IN SCHEMA public GRANT ALL ON SEQUENCES TO neon_superuser WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: cloud_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE cloud_admin IN SCHEMA public GRANT ALL ON TABLES TO neon_superuser WITH GRANT OPTION;


--
-- PostgreSQL database dump complete
--

