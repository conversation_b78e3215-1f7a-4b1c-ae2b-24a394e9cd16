--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5
-- Dumped by pg_dump version 17.5 (Debian 17.5-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: drizzle; Type: SCHEMA; Schema: -; Owner: neondb_owner
--

CREATE SCHEMA drizzle;


ALTER SCHEMA drizzle OWNER TO neondb_owner;

--
-- Name: belongTo; Type: TYPE; Schema: public; Owner: neondb_owner
--

CREATE TYPE public."belongTo" AS ENUM (
    'lobe',
    'admin'
);


ALTER TYPE public."belongTo" OWNER TO neondb_owner;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: __drizzle_migrations; Type: TABLE; Schema: drizzle; Owner: neondb_owner
--

CREATE TABLE drizzle.__drizzle_migrations (
    id integer NOT NULL,
    hash text NOT NULL,
    created_at bigint
);


ALTER TABLE drizzle.__drizzle_migrations OWNER TO neondb_owner;

--
-- Name: __drizzle_migrations_id_seq; Type: SEQUENCE; Schema: drizzle; Owner: neondb_owner
--

CREATE SEQUENCE drizzle.__drizzle_migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE drizzle.__drizzle_migrations_id_seq OWNER TO neondb_owner;

--
-- Name: __drizzle_migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: drizzle; Owner: neondb_owner
--

ALTER SEQUENCE drizzle.__drizzle_migrations_id_seq OWNED BY drizzle.__drizzle_migrations.id;


--
-- Name: feature; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.feature (
    id text NOT NULL,
    name text NOT NULL,
    code text NOT NULL,
    category text,
    description text,
    belong_to public."belongTo",
    accessed_at timestamp with time zone DEFAULT now() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.feature OWNER TO neondb_owner;

--
-- Name: license; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.license (
    id text NOT NULL,
    organization_id text NOT NULL,
    version_id text NOT NULL,
    start_time timestamp without time zone NOT NULL,
    end_time timestamp without time zone NOT NULL,
    enabled boolean DEFAULT true NOT NULL,
    accessed_at timestamp with time zone DEFAULT now() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.license OWNER TO neondb_owner;

--
-- Name: organization; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.organization (
    id text NOT NULL,
    chinese_name text NOT NULL,
    english_name text NOT NULL,
    logo text,
    scale text,
    industry text,
    country text,
    description text,
    assigner jsonb,
    lobe_owner jsonb NOT NULL,
    license_id text,
    accessed_at timestamp with time zone DEFAULT now() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.organization OWNER TO neondb_owner;

--
-- Name: version; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.version (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    max_users integer,
    max_version text,
    min_version text,
    is_active boolean DEFAULT true NOT NULL,
    accessed_at timestamp with time zone DEFAULT now() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.version OWNER TO neondb_owner;

--
-- Name: version_feature; Type: TABLE; Schema: public; Owner: neondb_owner
--

CREATE TABLE public.version_feature (
    version_id text NOT NULL,
    feature_id text NOT NULL,
    grant_type text NOT NULL,
    accessed_at timestamp with time zone DEFAULT now() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.version_feature OWNER TO neondb_owner;

--
-- Name: __drizzle_migrations id; Type: DEFAULT; Schema: drizzle; Owner: neondb_owner
--

ALTER TABLE ONLY drizzle.__drizzle_migrations ALTER COLUMN id SET DEFAULT nextval('drizzle.__drizzle_migrations_id_seq'::regclass);


--
-- Data for Name: __drizzle_migrations; Type: TABLE DATA; Schema: drizzle; Owner: neondb_owner
--

COPY drizzle.__drizzle_migrations (id, hash, created_at) FROM stdin;
3	8f9489bd4a202eef1010a02472e3eed01fa9f671cb39712534c17db27ec8189a	1753285551898
\.


--
-- Data for Name: feature; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.feature (id, name, code, category, description, belong_to, accessed_at, created_at, updated_at) FROM stdin;
feat_AbDRuV9vwytT	总览	Overview	页面	\N	admin	2025-07-26 08:33:00.830018+00	2025-07-26 08:33:00.830018+00	2025-07-26 08:33:00.830018+00
feat_rLU0woy3maz0	用户管理	Users	模块	\N	admin	2025-07-26 08:33:28.370545+00	2025-07-26 08:33:28.370545+00	2025-07-26 08:33:28.370545+00
feat_EsAV7sYP95zj	市场管理	Market	模块	\N	admin	2025-07-26 08:34:52.474959+00	2025-07-26 08:34:52.474959+00	2025-07-26 08:34:52.474959+00
feat_zvWgrlEjU7JG	市场概览	MarketDashboard	页面	\N	admin	2025-07-26 08:35:12.146732+00	2025-07-26 08:35:12.146732+00	2025-07-26 08:35:12.146732+00
feat_nrP4kel5J5ui	订阅	Subscription	模块	\N	admin	2025-07-26 08:35:56.221824+00	2025-07-26 08:35:56.221824+00	2025-07-26 08:35:56.221824+00
feat_iN4dNxhWkTmh	插件管理	MarketPlugin	页面	\N	admin	2025-07-26 08:35:41.930277+00	2025-07-26 08:35:41.930277+00	2025-07-26 08:36:50.939+00
feat_FwTU8CEmiVpS	订单管理	SubscriptionOrders	页面	\N	admin	2025-07-26 08:37:02.978071+00	2025-07-26 08:37:02.978071+00	2025-07-26 08:37:02.978071+00
feat_1eMpKx8I4vmu	预算管理	SubscriptionBudgets	页面	\N	admin	2025-07-26 08:37:20.467496+00	2025-07-26 08:37:20.467496+00	2025-07-26 08:37:20.467496+00
feat_zIqNipEtv32Z	渠道管理	Channel	模块	\N	admin	2025-07-26 08:37:36.21219+00	2025-07-26 08:37:36.21219+00	2025-07-26 08:37:36.21219+00
feat_MuT4s0hqQ8nN	渠道管理	ChannelManagement	页面	\N	admin	2025-07-26 08:37:45.492398+00	2025-07-26 08:37:45.492398+00	2025-07-26 08:37:45.492398+00
feat_XslZklxMma9d	模型管理	ModelManagement	页面	\N	admin	2025-07-26 08:37:55.711326+00	2025-07-26 08:37:55.711326+00	2025-07-26 08:37:55.711326+00
feat_eQNTFt1gSqTp	控制台	Console	模块	\N	admin	2025-07-26 08:38:12.365216+00	2025-07-26 08:38:12.365216+00	2025-07-26 08:38:12.365216+00
feat_mNUOH0lGSzZL	Webhook	ConsoleWebhook	页面	\N	admin	2025-07-26 08:38:28.713082+00	2025-07-26 08:38:28.713082+00	2025-07-26 08:38:28.713082+00
feat_93PUJWnhAoaO	数据迁移	ConsoleMigration	页面	\N	admin	2025-07-26 08:38:39.968816+00	2025-07-26 08:38:39.968816+00	2025-07-26 08:38:39.968816+00
feat_2O0qti0fIiUX	其他	Others	模块	\N	admin	2025-07-26 08:38:49.925173+00	2025-07-26 08:38:49.925173+00	2025-07-26 08:38:49.925173+00
feat_qjYagFyvyrCQ	角色权限管理	OthersRbac	页面	\N	admin	2025-07-26 08:39:00.486446+00	2025-07-26 08:39:00.486446+00	2025-07-26 08:39:00.486446+00
\.


--
-- Data for Name: license; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.license (id, organization_id, version_id, start_time, end_time, enabled, accessed_at, created_at, updated_at) FROM stdin;
lic_GGLnajq4lPlJ	org_8sJR8W0eWkv3	ver_ojnnMOmSh1QD	2025-07-23 16:00:00	2026-07-30 16:00:00	t	2025-07-24 15:11:30.362401+00	2025-07-24 15:11:30.362401+00	2025-07-24 16:51:36.965+00
lic_15ppwmiF5lRQ	org_McjVfNdpbmu6	ver_ojnnMOmSh1QD	2025-07-27 16:00:00	2025-07-30 16:00:00	t	2025-07-27 12:06:05.334497+00	2025-07-27 12:06:05.334497+00	2025-07-27 12:06:05.334497+00
\.


--
-- Data for Name: organization; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.organization (id, chinese_name, english_name, logo, scale, industry, country, description, assigner, lobe_owner, license_id, accessed_at, created_at, updated_at) FROM stdin;
org_8sJR8W0eWkv3	影和测试组织	yinghe-test	\N	\N	\N	中国	\N	{}	{}	\N	2025-07-24 15:04:38.964281+00	2025-07-24 15:04:38.964281+00	2025-07-26 05:43:56.637+00
org_McjVfNdpbmu6	czcz	czxczxczx	\N	\N	\N	\N	\N	{}	{}	\N	2025-07-26 06:09:43.044643+00	2025-07-26 06:09:43.044643+00	2025-07-26 06:09:43.044643+00
\.


--
-- Data for Name: version; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.version (id, name, description, max_users, max_version, min_version, is_active, accessed_at, created_at, updated_at) FROM stdin;
ver_ojnnMOmSh1QD	企业版	\N	\N	\N	\N	t	2025-07-24 14:36:12.15772+00	2025-07-24 14:36:12.15772+00	2025-07-24 15:58:53.549+00
\.


--
-- Data for Name: version_feature; Type: TABLE DATA; Schema: public; Owner: neondb_owner
--

COPY public.version_feature (version_id, feature_id, grant_type, accessed_at, created_at, updated_at) FROM stdin;
ver_ojnnMOmSh1QD	feat_AbDRuV9vwytT	grant	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00
ver_ojnnMOmSh1QD	feat_zIqNipEtv32Z	grant	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00
ver_ojnnMOmSh1QD	feat_MuT4s0hqQ8nN	grant	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00
ver_ojnnMOmSh1QD	feat_XslZklxMma9d	grant	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00
ver_ojnnMOmSh1QD	feat_2O0qti0fIiUX	grant	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00
ver_ojnnMOmSh1QD	feat_qjYagFyvyrCQ	grant	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00
ver_ojnnMOmSh1QD	feat_rLU0woy3maz0	grant	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00	2025-07-27 14:48:53.481562+00
\.


--
-- Name: __drizzle_migrations_id_seq; Type: SEQUENCE SET; Schema: drizzle; Owner: neondb_owner
--

SELECT pg_catalog.setval('drizzle.__drizzle_migrations_id_seq', 3, true);


--
-- Name: __drizzle_migrations __drizzle_migrations_pkey; Type: CONSTRAINT; Schema: drizzle; Owner: neondb_owner
--

ALTER TABLE ONLY drizzle.__drizzle_migrations
    ADD CONSTRAINT __drizzle_migrations_pkey PRIMARY KEY (id);


--
-- Name: feature feature_code_unique; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.feature
    ADD CONSTRAINT feature_code_unique UNIQUE (code);


--
-- Name: feature feature_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.feature
    ADD CONSTRAINT feature_pkey PRIMARY KEY (id);


--
-- Name: license license_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.license
    ADD CONSTRAINT license_pkey PRIMARY KEY (id);


--
-- Name: organization organization_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.organization
    ADD CONSTRAINT organization_pkey PRIMARY KEY (id);


--
-- Name: version_feature version_feature_unique; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.version_feature
    ADD CONSTRAINT version_feature_unique UNIQUE (version_id, feature_id);


--
-- Name: version version_pkey; Type: CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.version
    ADD CONSTRAINT version_pkey PRIMARY KEY (id);


--
-- Name: license license_version_id_version_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.license
    ADD CONSTRAINT license_version_id_version_id_fk FOREIGN KEY (version_id) REFERENCES public.version(id);


--
-- Name: version_feature version_feature_feature_id_feature_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.version_feature
    ADD CONSTRAINT version_feature_feature_id_feature_id_fk FOREIGN KEY (feature_id) REFERENCES public.feature(id);


--
-- Name: version_feature version_feature_version_id_version_id_fk; Type: FK CONSTRAINT; Schema: public; Owner: neondb_owner
--

ALTER TABLE ONLY public.version_feature
    ADD CONSTRAINT version_feature_version_id_version_id_fk FOREIGN KEY (version_id) REFERENCES public.version(id);


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: cloud_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE cloud_admin IN SCHEMA public GRANT ALL ON SEQUENCES TO neon_superuser WITH GRANT OPTION;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: cloud_admin
--

ALTER DEFAULT PRIVILEGES FOR ROLE cloud_admin IN SCHEMA public GRANT ALL ON TABLES TO neon_superuser WITH GRANT OPTION;


--
-- PostgreSQL database dump complete
--

